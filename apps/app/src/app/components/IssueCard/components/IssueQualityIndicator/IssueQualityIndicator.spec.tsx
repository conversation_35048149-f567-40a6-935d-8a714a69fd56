import createMatchMedia from 'tests/create-match-media';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { IssueQualityIndicator } from './IssueQualityIndicator';

describe('<IssueQualityIndicator />', () => {
  describe("when qualityScore is null", () => {
    it('renders nothing', () => {
      render(<IssueQualityIndicator qualityScore={null} />);

      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });
  })

  describe("when qualityScore is > 30", () => {
    it('renders an empty div', () => {
      const { container } = render(<IssueQualityIndicator qualityScore={35} className='test-class' />);

      expect(container.firstChild).toHaveClass('test-class');
      expect(container.firstChild).toBeEmptyDOMElement();
    });
  })

  describe("when qualityScore is < 30", () => {
    describe("and < 20", () => {
      it('renders a progress bar with the correct color', () => {
        const { container } = render(<IssueQualityIndicator qualityScore={15} />);

        const svg = container.querySelector('svg');
        expect(svg).toBeInTheDocument();

        const progressCircle = container.querySelector('circle.stroke-danger-bold');
        expect(progressCircle).toBeInTheDocument();
      });
    });

    describe("and >= 20", () => {
      it('renders a progress bar with the correct color', () => {
        const { container } = render(<IssueQualityIndicator qualityScore={25} />);

        const svg = container.querySelector('svg');
        expect(svg).toBeInTheDocument();

        // Check for warning color (stroke-warning-bold)
        const progressCircle = container.querySelector('circle.stroke-warning-bold');
        expect(progressCircle).toBeInTheDocument();
      });
    });
  })

  describe("when screen size is large", () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(1024);
    });

    describe("when user hovers over the progress bar", () => {
      it('renders the popover', async () => {
        const user = userEvent.setup();
        render(<IssueQualityIndicator qualityScore={25} />);

        const progressBarContainer = screen.getByRole('button');
        await user.hover(progressBarContainer);

        await waitFor(() => {
          expect(screen.getByRole('dialog')).toBeInTheDocument();
        });
      });
    });
  });

  describe("when screen size is small", () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(620);
    });

    describe("when user clicks on the progress bar", () => {
      it('renders the popover', async () => {
        const user = userEvent.setup();
        render(<IssueQualityIndicator qualityScore={25} />);

        const progressBarContainer = screen.getByRole('button');
        await user.click(progressBarContainer);

        await waitFor(() => {
          expect(screen.getByRole('dialog')).toBeInTheDocument();
        });
      });
    });
  });
});