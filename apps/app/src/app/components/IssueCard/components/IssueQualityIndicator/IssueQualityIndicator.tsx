import { useMessageGetter } from "@messageformat/react";
import type { IssueDetailsBasicSchema } from "@shape-construction/api/src/types";
import Popover from "@shape-construction/arch-ui/src/Popover";
import { ProgressBarRoot } from "@shape-construction/arch-ui/src/ProgressBar/ProgressBar";
import { breakpoints } from "@shape-construction/arch-ui/src/utils/breakpoints";
import { cn } from "@shape-construction/arch-ui/src/utils/classes";
import { useMediaQuery } from "@shape-construction/hooks";
import { useState, useMemo } from "react";

type IssueQualityIndicatorProps = {
    className?: string;
    qualityScore: IssueDetailsBasicSchema['qualityScore'];
};

const QUALITY_THRESHOLDS = {
    DANGER: 19,
    WARNING: 30,
} as const;

enum QualityCategory {
    NOT_USEFUL = 'notUseful',
    THE_BASICS = 'theBasics',
}

type ProgressBarColor = 'danger' | 'warning' | 'success';

function getQualityCategory(qualityScore: number): QualityCategory {
    if (qualityScore < QUALITY_THRESHOLDS.DANGER) {
        return QualityCategory.NOT_USEFUL;
    }
    return QualityCategory.THE_BASICS;
}

function getProgressBarColor(qualityScore: number): ProgressBarColor {
    if (qualityScore < QUALITY_THRESHOLDS.DANGER) return 'danger';
    if (qualityScore < QUALITY_THRESHOLDS.WARNING) return 'warning';
    return 'success';
}

export const IssueQualityIndicator: React.FC<IssueQualityIndicatorProps> = ({
    className,
    qualityScore
}) => {
    const [issueQualityPopoverOpen, setIssueQualityPopoverOpen] = useState(false);
    const isLargeScreen = useMediaQuery(breakpoints.up('md'));
    const messages = useMessageGetter('issue.list.qualityIndicators');


    const qualityData = useMemo(() => {
        if (qualityScore === null || qualityScore >= QUALITY_THRESHOLDS.WARNING) {
            return null;
        }

        const category = getQualityCategory(qualityScore);

        return {
            category,
            score: qualityScore,
            title: messages(`${category}.title`),
            scoreRange: messages(`${category}.scoreRange`),
            description: messages(`${category}.description`),
            color: getProgressBarColor(qualityScore),
        };
    }, [qualityScore, messages]);


    if (!qualityData) {
        return <div className={className} />;
    }

    const handlePointerEnter = () => {
        if (isLargeScreen) {
            setIssueQualityPopoverOpen(true);
        }
    };

    const handlePointerLeave = () => {
        if (isLargeScreen) {
            setIssueQualityPopoverOpen(false);
        }
    };

    const handleClick = (e: React.MouseEvent) => {
        e.preventDefault();
        setIssueQualityPopoverOpen(!issueQualityPopoverOpen);
    };

    return (
        <div
            className={cn(
                className,
                'flex justify-center items-center rounded-full w-auto p-0.5 hover:bg-neutral-subtle'
            )}
            onPointerEnter={handlePointerEnter}
            onPointerLeave={handlePointerLeave}
        >
            <Popover
                open={issueQualityPopoverOpen}
                onOpenChange={setIssueQualityPopoverOpen}
            >
                <Popover.Trigger
                    onClick={handleClick}
                >
                    <ProgressBarRoot
                        progress={qualityData.score}
                        size="small"
                        color={qualityData.color}
                        variant="donut"
                    />
                </Popover.Trigger>
                <Popover.Content side="bottom" align="center">
                    <div
                        id="quality-popover-content"
                        className="flex flex-col gap-1 text-sm leading-6"
                    >
                        <div className="font-semibold">
                            {qualityData.title}
                        </div>
                        <div className="font-bold">
                            {qualityData.scoreRange}
                        </div>
                        <div className="font-normal text-neutral">
                            {qualityData.description}
                        </div>
                    </div>
                </Popover.Content>
            </Popover>
        </div>
    );
};